import { useEffect, useRef, useCallback } from "react";
import { useAppContext } from "@/context/app-context";

interface UseInfiniteScrollOptions {
  threshold?: number; // Distance from top to trigger load more (in pixels)
  batchSize?: number; // Number of messages to load per batch
  enabled?: boolean; // Whether infinite scroll is enabled
}

export function useInfiniteScroll({
  threshold = 100,
  batchSize = 25,
  enabled = true,
}: UseInfiniteScrollOptions = {}) {
  const { state, dispatch } = useAppContext();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const loadingRef = useRef<HTMLDivElement>(null);
  const isLoadingRef = useRef(false);
  const previousScrollHeight = useRef(0);

  // Load more messages function
  const loadMoreMessages = useCallback(() => {
    if (
      !enabled ||
      isLoadingRef.current ||
      state.isLoadingMoreMessages ||
      !state.hasMoreMessages
    ) {
      return;
    }

    isLoadingRef.current = true;
    dispatch({ type: "SET_LOADING_MORE_MESSAGES", payload: true });

    // Simulate async loading with a small delay to prevent rapid firing
    setTimeout(() => {
      dispatch({ type: "LOAD_MORE_MESSAGES", payload: batchSize });
      isLoadingRef.current = false;
    }, 100);
  }, [enabled, state.isLoadingMoreMessages, state.hasMoreMessages, batchSize, dispatch]);

  // Intersection Observer for detecting when user scrolls near the top
  useEffect(() => {
    if (!enabled || !loadingRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && state.hasMoreMessages) {
          loadMoreMessages();
        }
      },
      {
        root: scrollContainerRef.current,
        rootMargin: `${threshold}px 0px 0px 0px`,
        threshold: 0.1,
      }
    );

    observer.observe(loadingRef.current);

    return () => {
      observer.disconnect();
    };
  }, [enabled, threshold, state.hasMoreMessages, loadMoreMessages]);

  // Maintain scroll position when new messages are loaded
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container || !enabled) return;

    // Store scroll position before new messages are added
    if (state.isLoadingMoreMessages) {
      previousScrollHeight.current = container.scrollHeight;
    }

    // Restore scroll position after new messages are loaded
    if (!state.isLoadingMoreMessages && previousScrollHeight.current > 0) {
      const newScrollHeight = container.scrollHeight;
      const heightDifference = newScrollHeight - previousScrollHeight.current;
      
      // Adjust scroll position to maintain the user's view
      container.scrollTop = container.scrollTop + heightDifference;
      previousScrollHeight.current = 0;
    }
  }, [state.isLoadingMoreMessages, enabled]);

  // Scroll to bottom when new messages are added (not from infinite scroll)
  const scrollToBottom = useCallback(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.scrollTop = container.scrollHeight;
    }
  }, []);

  // Check if user is near the bottom of the scroll container
  const isNearBottom = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) return false;

    const { scrollTop, scrollHeight, clientHeight } = container;
    return scrollHeight - scrollTop - clientHeight < 100;
  }, []);

  return {
    scrollContainerRef,
    loadingRef,
    loadMoreMessages,
    scrollToBottom,
    isNearBottom,
    hasMoreMessages: state.hasMoreMessages,
    isLoadingMoreMessages: state.isLoadingMoreMessages,
  };
}
