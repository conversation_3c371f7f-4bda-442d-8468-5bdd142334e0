import { useEffect, useRef, useCallback } from "react";
import { useAppContext } from "@/context/app-context";

interface UseInfiniteScrollOptions {
  threshold?: number; // Distance from top to trigger load more (in pixels)
  batchSize?: number; // Number of messages to load per batch
  enabled?: boolean; // Whether infinite scroll is enabled
}

export function useInfiniteScroll({
  threshold = 200,
  batchSize = 25,
  enabled = true,
}: UseInfiniteScrollOptions = {}) {
  const { state, dispatch } = useAppContext();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const loadingRef = useRef<HTMLDivElement>(null);
  const isLoadingRef = useRef(false);
  const previousScrollHeight = useRef(0);

  // Load more messages function
  const loadMoreMessages = useCallback(() => {
    if (
      !enabled ||
      isLoadingRef.current ||
      state.isLoadingMoreMessages ||
      !state.hasMoreMessages
    ) {
      return;
    }

    isLoadingRef.current = true;
    dispatch({ type: "SET_LOADING_MORE_MESSAGES", payload: true });

    // Simulate async loading with a small delay to prevent rapid firing
    setTimeout(() => {
      dispatch({ type: "LOAD_MORE_MESSAGES", payload: batchSize });
      isLoadingRef.current = false;
    }, 300);
  }, [
    enabled,
    state.isLoadingMoreMessages,
    state.hasMoreMessages,
    batchSize,
    dispatch,
  ]);

  // Scroll event listener for detecting when user scrolls near the top
  useEffect(() => {
    if (!enabled) return;

    const container = scrollContainerRef.current;
    if (!container) return;

    let animationId: number | null = null;
    let lastScrollTop = container.scrollTop;

    const handleScroll = () => {
      const { scrollTop } = container;

      // Only process if scroll position changed significantly
      if (Math.abs(scrollTop - lastScrollTop) < 10) return;
      lastScrollTop = scrollTop;

      // Check if user is near the top (within threshold pixels)
      const isNearTop = scrollTop < threshold;

      // Only load more if we're near the top and have more messages
      if (isNearTop && state.hasMoreMessages && !state.isLoadingMoreMessages) {
        loadMoreMessages();
      }
    };

    // Use requestAnimationFrame for better performance
    const throttledScroll = () => {
      if (animationId) return;

      animationId = requestAnimationFrame(() => {
        handleScroll();
        animationId = null;
      });
    };

    container.addEventListener("scroll", throttledScroll, { passive: true });

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      container.removeEventListener("scroll", throttledScroll);
    };
  }, [
    enabled,
    threshold,
    state.hasMoreMessages,
    state.isLoadingMoreMessages,
    loadMoreMessages,
  ]);

  // Maintain scroll position when new messages are loaded
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container || !enabled) return;

    // Store scroll position before new messages are added
    if (state.isLoadingMoreMessages) {
      previousScrollHeight.current = container.scrollHeight;
    }

    // Restore scroll position after new messages are loaded
    if (!state.isLoadingMoreMessages && previousScrollHeight.current > 0) {
      const newScrollHeight = container.scrollHeight;
      const heightDifference = newScrollHeight - previousScrollHeight.current;

      // Adjust scroll position to maintain the user's view
      container.scrollTop = container.scrollTop + heightDifference;
      previousScrollHeight.current = 0;
    }
  }, [state.isLoadingMoreMessages, enabled]);

  // Scroll to bottom when new messages are added (not from infinite scroll)
  const scrollToBottom = useCallback(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.scrollTop = container.scrollHeight;
    }
  }, []);

  // Check if user is near the bottom of the scroll container
  const isNearBottom = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) return false;

    const { scrollTop, scrollHeight, clientHeight } = container;
    return scrollHeight - scrollTop - clientHeight < 100;
  }, []);

  return {
    scrollContainerRef,
    loadingRef,
    loadMoreMessages,
    scrollToBottom,
    isNearBottom,
    hasMoreMessages: state.hasMoreMessages,
    isLoadingMoreMessages: state.isLoadingMoreMessages,
  };
}
